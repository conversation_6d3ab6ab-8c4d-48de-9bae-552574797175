using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - SQL Server حصرياً مع دعم الإعدادات المحلية
    /// </summary>
    public static class DatabaseConfig
    {
        private static readonly string ConfigFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "database-config.json");

        /// <summary>
        /// إعدادات قاعدة البيانات
        /// </summary>
        public class DatabaseSettings
        {
            public string ServerName { get; set; } = "localhost";
            public string DatabaseName { get; set; } = "SFDSYS";
            public bool UseWindowsAuth { get; set; } = true;
            public string Username { get; set; } = "sa";
            public string Password { get; set; } = "";
        }

        /// <summary>
        /// الحصول على connection string لـ SQL Server
        /// </summary>
        public static string GetConnectionString()
        {
            var settings = LoadSettings();

            // استخدام SQL Server حصرياً
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;";
            }
        }

        /// <summary>
        /// تحميل الإعدادات من الملف أو متغيرات البيئة
        /// </summary>
        public static DatabaseSettings LoadSettings()
        {
            var settings = new DatabaseSettings();

            try
            {
                // محاولة تحميل من الملف المحلي أولاً
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    var fileSettings = JsonSerializer.Deserialize<DatabaseSettings>(json);
                    if (fileSettings != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحميل إعدادات قاعدة البيانات من الملف المحلي");
                        return fileSettings;
                    }
                }

                // إذا لم يوجد ملف، تحميل من متغيرات البيئة
                settings.ServerName = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
                settings.DatabaseName = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";
                settings.UseWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH") != "false";
                settings.Username = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "sa";
                settings.Password = Environment.GetEnvironmentVariable("SQL_PASSWORD") ?? "";

                // حفظ الإعدادات في ملف للمرة القادمة
                SaveSettings(settings);
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل إعدادات قاعدة البيانات من متغيرات البيئة وحفظها محلياً");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل إعدادات قاعدة البيانات: {ex.Message}");
            }

            return settings;
        }

        /// <summary>
        /// حفظ الإعدادات في ملف محلي
        /// </summary>
        public static void SaveSettings(DatabaseSettings settings)
        {
            try
            {
                var directory = Path.GetDirectoryName(ConfigFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(ConfigFilePath, json);

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ إعدادات قاعدة البيانات في: {ConfigFilePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود ملف الإعدادات
        /// </summary>
        public static bool HasConfigFile()
        {
            return File.Exists(ConfigFilePath);
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                var connectionString = GetConnectionString();
                using var connection = new Microsoft.Data.SqlClient.SqlConnection(connectionString);
                await connection.OpenAsync();
                System.Diagnostics.Debug.WriteLine($"✅ تم اختبار الاتصال بقاعدة البيانات بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل اختبار الاتصال بقاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات للقيم الافتراضية
        /// </summary>
        public static void ResetToDefaults()
        {
            var defaultSettings = new DatabaseSettings();
            SaveSettings(defaultSettings);
            System.Diagnostics.Debug.WriteLine($"✅ تم إعادة تعيين إعدادات قاعدة البيانات للقيم الافتراضية");
        }
    }
}
