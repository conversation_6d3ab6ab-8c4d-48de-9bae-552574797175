using System;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - SQL Server حصرياً
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// الحصول على connection string لـ SQL Server
        /// </summary>
        public static string GetConnectionString()
        {
            // يمكن تخصيص هذه القيم حسب الحاجة
            var serverName = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
            var databaseName = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";
            var useWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH") != "false";

            if (useWindowsAuth)
            {
                return $"Server={serverName};Database={databaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                var username = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "sa";
                var password = Environment.GetEnvironmentVariable("SQL_PASSWORD") ?? "";
                return $"Server={serverName};Database={databaseName};User Id={username};Password={password};TrustServerCertificate=true;";
            }
        }

    }
}
