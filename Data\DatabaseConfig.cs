using System;
using System.IO;
using System.Text.Json;

namespace DriverManagementSystem.Data
{
    /// <summary>
    /// إعدادات قاعدة البيانات - SQL Server حصرياً مع دعم الإعدادات المحلية
    /// </summary>
    public static class DatabaseConfig
    {
        private static readonly string ConfigFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "database-config.json");
        private static readonly string LocalDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "SFDSYS.db");

        /// <summary>
        /// إعدادات قاعدة البيانات
        /// </summary>
        public class DatabaseSettings
        {
            public string ServerName { get; set; } = "localhost";
            public string DatabaseName { get; set; } = "SFDSYS";
            public bool UseWindowsAuth { get; set; } = true;
            public string Username { get; set; } = "sa";
            public string Password { get; set; } = "";
            public bool UseLocalDatabase { get; set; } = false;
            public string LocalDatabasePath { get; set; } = "";
        }

        /// <summary>
        /// الحصول على connection string لـ SQL Server أو SQLite
        /// </summary>
        public static string GetConnectionString()
        {
            var settings = LoadSettings();

            // إذا كان هناك قاعدة بيانات محلية، استخدمها
            if (settings.UseLocalDatabase && File.Exists(settings.LocalDatabasePath))
            {
                return $"Data Source={settings.LocalDatabasePath}";
            }

            // إذا كان هناك ملف قاعدة بيانات محلي في مجلد Data، استخدمه
            if (File.Exists(LocalDbPath))
            {
                settings.UseLocalDatabase = true;
                settings.LocalDatabasePath = LocalDbPath;
                SaveSettings(settings);
                return $"Data Source={LocalDbPath}";
            }

            // استخدام SQL Server
            if (settings.UseWindowsAuth)
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};Trusted_Connection=true;TrustServerCertificate=true;";
            }
            else
            {
                return $"Server={settings.ServerName};Database={settings.DatabaseName};User Id={settings.Username};Password={settings.Password};TrustServerCertificate=true;";
            }
        }

        /// <summary>
        /// تحميل الإعدادات من الملف أو متغيرات البيئة
        /// </summary>
        public static DatabaseSettings LoadSettings()
        {
            var settings = new DatabaseSettings();

            try
            {
                // محاولة تحميل من الملف المحلي أولاً
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    var fileSettings = JsonSerializer.Deserialize<DatabaseSettings>(json);
                    if (fileSettings != null)
                    {
                        return fileSettings;
                    }
                }

                // إذا لم يوجد ملف، تحميل من متغيرات البيئة
                settings.ServerName = Environment.GetEnvironmentVariable("SQL_SERVER_NAME") ?? "localhost";
                settings.DatabaseName = Environment.GetEnvironmentVariable("SQL_DATABASE_NAME") ?? "SFDSYS";
                settings.UseWindowsAuth = Environment.GetEnvironmentVariable("SQL_USE_WINDOWS_AUTH") != "false";
                settings.Username = Environment.GetEnvironmentVariable("SQL_USERNAME") ?? "sa";
                settings.Password = Environment.GetEnvironmentVariable("SQL_PASSWORD") ?? "";

                // حفظ الإعدادات في ملف للمرة القادمة
                SaveSettings(settings);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل إعدادات قاعدة البيانات: {ex.Message}");
            }

            return settings;
        }

        /// <summary>
        /// حفظ الإعدادات في ملف محلي
        /// </summary>
        public static void SaveSettings(DatabaseSettings settings)
        {
            try
            {
                var directory = Path.GetDirectoryName(ConfigFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(ConfigFilePath, json);

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ إعدادات قاعدة البيانات في: {ConfigFilePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة بيانات محلية
        /// </summary>
        public static bool HasLocalDatabase()
        {
            return File.Exists(LocalDbPath);
        }

        /// <summary>
        /// إنشاء قاعدة بيانات محلية
        /// </summary>
        public static void CreateLocalDatabase()
        {
            try
            {
                var directory = Path.GetDirectoryName(LocalDbPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // إنشاء ملف قاعدة بيانات فارغ
                if (!File.Exists(LocalDbPath))
                {
                    File.Create(LocalDbPath).Dispose();
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة بيانات محلية في: {LocalDbPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات المحلية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على نوع قاعدة البيانات المستخدمة
        /// </summary>
        public static string GetDatabaseType()
        {
            var settings = LoadSettings();
            if (settings.UseLocalDatabase || File.Exists(LocalDbPath))
            {
                return "SQLite";
            }
            return "SQL Server";
        }
    }
}
