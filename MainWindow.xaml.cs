using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Views;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem;

public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;
    private Services.AutoBackupService? _autoBackupService;

    public MainWindow()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🏠 بدء إنشاء النافذة الرئيسية");

            InitializeComponent();
            System.Diagnostics.Debug.WriteLine("✅ تم تهيئة مكونات النافذة");

            var authService = new AuthenticationService();
            _viewModel = new MainViewModel(authService);
            DataContext = _viewModel;
            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء ViewModel");

            _viewModel.LogoutRequested += OnLogoutRequested;
            System.Diagnostics.Debug.WriteLine("✅ تم تسجيل أحداث تسجيل الخروج");

            // إضافة البيانات الأساسية (بشكل آمن)
            System.Diagnostics.Debug.WriteLine("🔄 بدء تهيئة قاعدة البيانات");
            Task.Run(async () =>
            {
                try
                {
                    await InitializeDatabaseAsync();
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تهيئة قاعدة البيانات: {dbEx.Message}");
                    // لا نوقف النافذة بسبب خطأ في قاعدة البيانات
                }
            });

            // تشغيل خدمة النسخ الاحتياطي التلقائي (بشكل آمن)
            System.Diagnostics.Debug.WriteLine("💾 تشغيل خدمة النسخ الاحتياطي");
            try
            {
                InitializeAutoBackupService();
            }
            catch (Exception backupEx)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في خدمة النسخ الاحتياطي: {backupEx.Message}");
                // لا نوقف النافذة بسبب خطأ في النسخ الاحتياطي
            }

            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء النافذة الرئيسية بنجاح");

            // إضافة معالج لحدث تحميل النافذة
            this.Loaded += MainWindow_Loaded;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء النافذة الرئيسية: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");

            // عرض رسالة خطأ مفصلة
            var errorMessage = $"خطأ في إنشاء النافذة الرئيسية:\n\n{ex.Message}";
            if (ex.InnerException != null)
            {
                errorMessage += $"\n\nالخطأ الداخلي:\n{ex.InnerException.Message}";
            }

            MessageBox.Show(errorMessage, "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);

            // إغلاق التطبيق بأمان
            Application.Current.Shutdown();
        }
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🏠 تم تحميل النافذة الرئيسية بنجاح");
            MessageBox.Show("النافذة الرئيسية تم تحميلها بنجاح!", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل النافذة الرئيسية: {ex.Message}");
            MessageBox.Show($"خطأ في تحميل النافذة الرئيسية: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🌱 بدء تهيئة قاعدة البيانات...");

            // التحقق من وجود البيانات أولاً
            using var dataService = new DatabaseService();
            var existingDrivers = await dataService.GetDriversAsync();
            var existingSectors = await dataService.GetSectorsAsync();
            var existingProjects = await dataService.GetProjectsAsync();

            // إضافة القطاعات فقط إذا لم تكن موجودة
            if (existingSectors?.Count == 0)
            {
                var basicSeeder = new DatabaseSeeder();
                await basicSeeder.SeedSectorsAsync();
                basicSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة القطاعات الأساسية");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"📋 القطاعات موجودة مسبقاً ({existingSectors?.Count} قطاع)");
            }

            // تحديث أكواد القطاعات لتتطابق مع Excel
            try
            {
                using var context = new ApplicationDbContext();
                var sectorUpdater = new Migrations.UpdateSectorCodes(context);
                await sectorUpdater.ApplyAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم تحديث أكواد القطاعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تحديث أكواد القطاعات: {ex.Message}");
            }

            // إضافة المشاريع فقط إذا لم تكن موجودة
            if (existingProjects?.Count == 0)
            {
                var basicSeeder = new DatabaseSeeder();
                await basicSeeder.SeedProjectsAsync();
                basicSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة المشاريع الأساسية");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"📁 المشاريع موجودة مسبقاً ({existingProjects?.Count} مشروع)");
            }

            // إضافة البيانات الحقيقية فقط إذا لم تكن موجودة
            if (existingDrivers?.Count == 0)
            {
                var realSeeder = new RealDataSeeder();
                await realSeeder.SeedRealDataAsync();
                realSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة البيانات الحقيقية");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"🚗 السائقين موجودين مسبقاً ({existingDrivers?.Count} سائق) - لن يتم إعادة الإضافة");
            }

            // اختبار قراءة البيانات النهائية
            var finalSectors = await dataService.GetSectorsAsync();
            var finalOfficers = await dataService.GetOfficersAsync();
            var finalDrivers = await dataService.GetDriversAsync();

            System.Diagnostics.Debug.WriteLine($"📊 إحصائيات قاعدة البيانات النهائية:");
            System.Diagnostics.Debug.WriteLine($"   - القطاعات: {finalSectors?.Count}");
            System.Diagnostics.Debug.WriteLine($"   - الموظفين: {finalOfficers?.Count}");
            System.Diagnostics.Debug.WriteLine($"   - السائقين: {finalDrivers?.Count}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
        }
    }

    private async Task SafeInitializeProfessionalTemplatesAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية بطريقة آمنة...");

            // تفعيل النماذج الاحترافية بدون خدمة خارجية
            await Task.Run(() =>
            {
                try
                {
                    // تفعيل الإعدادات الأساسية
                    DriverManagementSystem.Properties.Settings.Default.ProfessionalTemplatesActivated = true;
                    DriverManagementSystem.Properties.Settings.Default.ActivationDate = DateTime.Now.ToString();
                    DriverManagementSystem.Properties.Settings.Default.Save();

                    System.Diagnostics.Debug.WriteLine("✅ تم تفعيل النماذج الاحترافية بنجاح!");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات النماذج الاحترافية: {ex.Message}");
                }
            });

            // تم إزالة الرسالة المزعجة
            System.Diagnostics.Debug.WriteLine("🎨 تم تفعيل النماذج الاحترافية بنجاح!");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في تفعيل النماذج الاحترافية: {ex.Message}");
        }
    }

    private async Task InitializeProfessionalTemplatesAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية...");

            var templatesService = new ProfessionalTemplatesService();

            // التحقق من حالة التفعيل السابقة
            if (templatesService.AreProfessionalTemplatesActivated())
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ النماذج الاحترافية مفعلة مسبقاً");
                return;
            }

            // تفعيل النماذج الاحترافية
            var success = await templatesService.ActivateAllProfessionalTemplatesAsync();

            if (success)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل جميع النماذج الاحترافية بنجاح!");

                // تم إزالة الرسالة المزعجة نهائياً
                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل جميع النماذج الاحترافية بنجاح!");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("⚠️ فشل في تفعيل بعض النماذج الاحترافية");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل النماذج الاحترافية: {ex.Message}");
        }
    }



    private void OnLogoutRequested(object? sender, System.EventArgs e)
    {
        var loginWindow = new Views.LoginWindow();
        loginWindow.Show();
        this.Close();
    }



    /// <summary>
    /// فتح نافذة إعداد قاعدة البيانات الاحترافية
    /// </summary>
    private void DatabaseSetupButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var setupWindow = new Views.DatabaseConnectionWindow();
            var result = setupWindow.ShowDialog();

            if (result == true && setupWindow.ConnectionSuccessful)
            {
                MessageBox.Show("تم حفظ إعدادات قاعدة البيانات بنجاح!", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إعداد قاعدة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// فتح نافذة إضافة الضباط الجدد
    /// </summary>
    private void AddOfficersButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addOfficersWindow = new Views.AddOfficersWindow();
            addOfficersWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة إضافة الضباط: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// فتح نافذة استعادة قاعدة البيانات
    /// </summary>
    private void DatabaseRestoreButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var restoreWindow = new DatabaseRestoreWindow();
            restoreWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة استعادة قاعدة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    /// <summary>
    /// تشغيل خدمة النسخ الاحتياطي التلقائي
    /// </summary>
    private void InitializeAutoBackupService()
    {
        try
        {
            _autoBackupService = new Services.AutoBackupService();
            _autoBackupService.Start();
            System.Diagnostics.Debug.WriteLine("🔄 تم تشغيل خدمة النسخ الاحتياطي التلقائي");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشغيل خدمة النسخ الاحتياطي: {ex.Message}");
        }
    }

    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        // منع إغلاق النافذة بدون تأكيد
        var result = MessageBox.Show("هل تريد إغلاق النظام؟", "تأكيد الإغلاق",
            MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.No)
        {
            e.Cancel = true;
            return;
        }

        base.OnClosing(e);
    }

    protected override void OnClosed(System.EventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔄 بدء إغلاق النافذة الرئيسية");
            _viewModel.LogoutRequested -= OnLogoutRequested;
            _autoBackupService?.Dispose();
            System.Diagnostics.Debug.WriteLine("✅ تم إغلاق جميع الخدمات بنجاح");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق الخدمات: {ex.Message}");
        }
        finally
        {
            base.OnClosed(e);
        }
    }
}