# نظام قاعدة البيانات المحمولة - الصندوق الاجتماعي للتنمية

## 🎯 الهدف
حل مشكلة عدم التعرف على قاعدة البيانات عند نقل النظام لجهاز آخر، وجعل النظام يتعرف تلقائياً على قواعد البيانات الموجودة.

## ✨ المميزات الجديدة

### 1. نظام الإعدادات المحلية
- **ملف الإعدادات**: `Data/database-config.json`
- **الحفظ التلقائي**: يتم حفظ إعدادات قاعدة البيانات محلياً مع النظام
- **النقل السهل**: عند نسخ النظام لجهاز آخر، تنتقل الإعدادات معه

### 2. اكتشاف قواعد البيانات التلقائي
- **البحث الذكي**: يبحث النظام تلقائياً عن قواعد بيانات SFDSYS في الخوادم المحلية
- **التحليل المتقدم**: يحلل كل قاعدة بيانات ويعرض معلومات مفصلة
- **الاختيار السهل**: واجهة بسيطة لاختيار قاعدة البيانات المناسبة

### 3. واجهة اختيار قاعدة البيانات
- **عرض شامل**: يعرض جميع قواعد البيانات المكتشفة مع تفاصيلها
- **التمييز الذكي**: يميز قواعد البيانات التي تحتوي على بيانات SFD
- **الاختبار المباشر**: اختبار الاتصال قبل الاختيار

## 🔧 كيفية العمل

### عند التشغيل الأول
1. **فحص الإعدادات**: يتحقق النظام من وجود ملف `database-config.json`
2. **البحث التلقائي**: إذا لم توجد إعدادات، يبدأ البحث عن قواعد البيانات
3. **عرض النتائج**: يعرض جميع قواعد البيانات المكتشفة في واجهة سهلة
4. **الاختيار والحفظ**: المستخدم يختار قاعدة البيانات ويتم حفظ الإعدادات

### عند النقل لجهاز آخر
1. **تحميل الإعدادات**: يحمل النظام الإعدادات من الملف المحلي
2. **اختبار الاتصال**: يختبر الاتصال بقاعدة البيانات المحفوظة
3. **البحث البديل**: إذا فشل الاتصال، يعرض نافذة اختيار قاعدة البيانات

## 📁 الملفات الجديدة

### 1. إعدادات قاعدة البيانات
```
Data/DatabaseConfig.cs - كلاس إدارة الإعدادات
Data/database-config.json - ملف الإعدادات المحلي
```

### 2. خدمة اكتشاف قواعد البيانات
```
Services/DatabaseDiscoveryService.cs - خدمة البحث والاكتشاف
```

### 3. واجهة اختيار قاعدة البيانات
```
Views/DatabaseSelectionWindow.xaml - واجهة الاختيار
Views/DatabaseSelectionWindow.xaml.cs - منطق الواجهة
```

## 🎮 طريقة الاستخدام

### للمستخدم العادي
1. شغل النظام
2. إذا ظهرت نافذة اختيار قاعدة البيانات:
   - انتظر انتهاء البحث
   - اختر قاعدة البيانات المناسبة (المميزة باللون الأخضر)
   - اضغط "اختيار قاعدة البيانات"
3. النظام سيحفظ الإعدادات ولن يطلب الاختيار مرة أخرى

### للمطور/المسؤول
```csharp
// تحميل الإعدادات
var settings = DatabaseConfig.LoadSettings();

// حفظ إعدادات جديدة
var newSettings = new DatabaseConfig.DatabaseSettings
{
    ServerName = "localhost",
    DatabaseName = "SFDSYS",
    UseWindowsAuth = true
};
DatabaseConfig.SaveSettings(newSettings);

// اختبار الاتصال
bool isConnected = await DatabaseConfig.TestConnectionAsync();
```

## 🔍 معلومات قواعد البيانات المعروضة

| العمود | الوصف |
|--------|--------|
| اسم الخادم | اسم خادم SQL Server |
| اسم قاعدة البيانات | اسم قاعدة البيانات |
| عدد الجداول | إجمالي عدد الجداول |
| عدد المستخدمين | عدد المستخدمين المسجلين |
| يحتوي على بيانات SFD | هل تحتوي على جداول النظام |
| الحالة | حالة الاتصال (متاح/غير متاح) |

## 🎨 المميزات البصرية

### الألوان المميزة
- **أخضر فاتح**: قواعد البيانات التي تحتوي على بيانات SFD
- **أزرق**: الصف المحدد
- **أبيض**: قواعد البيانات العادية

### الأيقونات
- ✅ اختيار قاعدة البيانات
- 🔄 إعادة البحث
- ⚙️ إعداد يدوي
- ❌ إلغاء

## 🛠️ الصيانة والاستكشاف

### حل المشاكل الشائعة

#### لا يظهر أي قاعدة بيانات
- تأكد من تشغيل SQL Server
- تأكد من وجود صلاحيات الوصول
- استخدم "إعداد يدوي" لإدخال البيانات يدوياً

#### خطأ في الاتصال
- تحقق من اسم الخادم
- تأكد من إعدادات Windows Authentication
- تحقق من إعدادات الشبكة والجدار الناري

### ملفات السجل
يتم كتابة رسائل التشخيص في:
```
System.Diagnostics.Debug.WriteLine()
```

## 📋 قائمة التحديثات

### الإصدار 2.1.0
- ✅ نظام الإعدادات المحلية
- ✅ اكتشاف قواعد البيانات التلقائي
- ✅ واجهة اختيار قاعدة البيانات
- ✅ حفظ الإعدادات مع النظام
- ✅ اختبار الاتصال التلقائي

### المميزات القادمة
- 🔄 نسخ احتياطي تلقائي لقاعدة البيانات
- 🔄 مزامنة قواعد البيانات المتعددة
- 🔄 إعدادات متقدمة للشبكة

## 📞 الدعم الفني

للحصول على المساعدة:
1. تحقق من ملف السجل
2. تأكد من إعدادات SQL Server
3. استخدم "إعداد يدوي" كحل بديل
4. تواصل مع فريق التطوير

---
**تم التطوير بواسطة**: فريق تطوير الصندوق الاجتماعي للتنمية  
**التاريخ**: يوليو 2025  
**الإصدار**: 2.1.0
