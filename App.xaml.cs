﻿using System;
using System.Configuration;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Services;
using DriverManagementSystem.Data;
using DriverManagementSystem.Migrations;

namespace DriverManagementSystem;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private async void Application_Startup(object sender, StartupEventArgs e)
    {
        try
        {
            // التحقق من وجود معامل لإضافة البيانات
            if (e.Args.Length > 0 && e.Args[0] == "--add-drivers")
            {
                await AddDriversData();
                Shutdown();
                return;
            }

            // فحص قاعدة البيانات والمستخدمين
            await CheckDatabaseAndUsers();

            // تنظيف رسائل السائق من مهام النزول
            await CleanDriverMessagesOnStartup();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في بدء تشغيل النظام: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.ToString()}",
                "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private async Task CheckDatabaseAndUsers()
    {
        try
        {
            // التحقق من إعدادات قاعدة البيانات أولاً
            if (!Data.DatabaseConfig.HasConfigFile())
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لا يوجد ملف إعدادات قاعدة البيانات - سيتم إظهار نافذة الإعداد");

                MessageBox.Show("مرحباً! يبدو أن هذا هو التشغيل الأول للنظام على هذا الجهاز.\nسيتم الآن إعداد الاتصال بقاعدة البيانات.",
                    "إعداد أولي", MessageBoxButton.OK, MessageBoxImage.Information);

                // إظهار نافذة إعداد قاعدة البيانات
                var dbSetupWindow = new Views.DatabaseConnectionWindow();
                var dbResult = dbSetupWindow.ShowDialog();

                if (dbResult != true)
                {
                    MessageBox.Show("يجب إعداد قاعدة البيانات لتشغيل النظام", "إعداد مطلوب",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    Shutdown();
                    return;
                }
            }

            // اختبار الاتصال بقاعدة البيانات
            var connectionTest = await Data.DatabaseConfig.TestConnectionAsync();
            if (!connectionTest)
            {
                System.Diagnostics.Debug.WriteLine("❌ فشل الاتصال بقاعدة البيانات - إظهار نافذة الإعداد");

                MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات المحفوظة.\nيرجى التحقق من إعدادات الاتصال.",
                    "خطأ في الاتصال", MessageBoxButton.OK, MessageBoxImage.Warning);

                var dbSetupWindow = new Views.DatabaseConnectionWindow();
                var dbResult = dbSetupWindow.ShowDialog();

                if (dbResult != true)
                {
                    MessageBox.Show("لا يمكن الاتصال بقاعدة البيانات. تأكد من إعدادات الاتصال.", "خطأ في الاتصال",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }
            }

            // نقل البيانات من SQLite إلى SQL Server إذا لزم الأمر
            await MigrateDataIfNeeded();

            using var context = new ApplicationDbContext();

            System.Diagnostics.Debug.WriteLine("🔧 جاري إعداد قاعدة البيانات...");

            // التأكد من إنشاء قاعدة البيانات والجداول
            var databaseCreated = await context.Database.EnsureCreatedAsync();
            if (databaseCreated)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات والجداول بنجاح");
            }

            // التحقق من وجود جدول FieldVisitItineraries وإنشاؤه فقط إذا لم يكن موجوداً
            await EnsureFieldVisitItinerariesTableExists(context);

            // فحص وجود مستخدمين
            var usersCount = await context.Users.CountAsync();

            if (usersCount == 0)
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ قاعدة البيانات جديدة - لا يوجد مستخدمين");

                MessageBox.Show(
                    "مرحباً! تم إعداد قاعدة البيانات بنجاح.\n\n" +
                    "الآن سيتم إنشاء المستخدم الأول للنظام.\n\n" +
                    "سيتم أيضاً إضافة البيانات الأساسية (القطاعات، السائقين، المشاريع).",
                    "إعداد النظام",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                // لا يوجد مستخدمين - إظهار نموذج الإعداد الأولي
                var setupWindow = new Views.InitialSetupWindow();
                var result = setupWindow.ShowDialog();

                if (result != true)
                {
                    // المستخدم ألغى الإعداد - إغلاق النظام
                    Shutdown();
                    return;
                }

                System.Diagnostics.Debug.WriteLine("⏳ جاري إضافة البيانات الأساسية...");

                // إضافة البيانات الأساسية بعد إنشاء المستخدم الأول
                var seeder = new InitialDataSeeder(context);
                await seeder.SeedBasicDataAsync();

                // إدراج بيانات السائقين الأولية
                await Data.SeedDriversData.SeedDriversAsync(context);

                System.Diagnostics.Debug.WriteLine("✅ تم إضافة جميع البيانات الأساسية");

                MessageBox.Show(
                    "تم إعداد النظام بنجاح!\n\n" +
                    "تم إضافة:\n" +
                    "• المستخدم الأول\n" +
                    "• القطاعات الأساسية\n" +
                    "• بيانات السائقين\n" +
                    "• المشاريع\n\n" +
                    "يمكنك الآن تسجيل الدخول والبدء في استخدام النظام.",
                    "تم الإعداد بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {usersCount} مستخدم في قاعدة البيانات");

                // حتى لو كان هناك مستخدمين، تأكد من إدراج بيانات السائقين
                await Data.SeedDriversData.SeedDriversAsync(context);
            }

            // إظهار نافذة تسجيل الدخول
            var loginWindow = new Views.LoginWindow();
            loginWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فحص قاعدة البيانات: {ex.Message}",
                "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);
            Shutdown();
        }
    }

    private async Task AddDriversData()
    {
        try
        {
            Console.WriteLine("🔄 بدء إضافة بيانات السائقين والمركبات والمشاريع...");

            var seeder = new Services.DatabaseSeeder();
            await seeder.SeedAllDataAsync();

            Console.WriteLine("✅ تم إضافة جميع البيانات بنجاح!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ في إضافة البيانات: {ex.Message}");
        }
    }

    /// <summary>
    /// نقل البيانات من SQLite إلى SQL Server إذا لزم الأمر
    /// </summary>
    private async Task MigrateDataIfNeeded()
    {
        try
        {
            // التحقق من وجود ملف SQLite
            var sqliteDbPath = Path.Combine(Directory.GetCurrentDirectory(), "Data", "SFDSYS.db");
            if (!File.Exists(sqliteDbPath))
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ لا يوجد ملف SQLite للنقل");
                return;
            }

            System.Diagnostics.Debug.WriteLine("🔄 بدء عملية نقل البيانات من SQLite إلى SQL Server...");

            var migrationService = new Services.DataMigrationService();
            var success = await migrationService.MigrateFromSqliteToSqlServerAsync();

            if (success)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم نقل البيانات بنجاح");

                // إنشاء نسخة احتياطية من ملف SQLite
                var backupPath = sqliteDbPath + ".backup_" + DateTime.Now.ToString("yyyyMMdd_HHmmss");
                File.Copy(sqliteDbPath, backupPath);
                System.Diagnostics.Debug.WriteLine($"📁 تم إنشاء نسخة احتياطية: {backupPath}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("❌ فشل في نقل البيانات");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في نقل البيانات: {ex.Message}");
        }
    }

    /// <summary>
    /// التحقق من وجود جدول FieldVisitItineraries وإنشاؤه فقط إذا لم يكن موجوداً
    /// </summary>
    private async Task EnsureFieldVisitItinerariesTableExists(ApplicationDbContext context)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🔍 فحص وجود جدول FieldVisitItineraries...");

            // التحقق من وجود الجدول
            var tableExists = false;
            try
            {
                // استخدام طريقة أفضل للتحقق من وجود الجدول
                var tableExistsQuery = @"
                    SELECT CASE WHEN EXISTS (
                        SELECT * FROM INFORMATION_SCHEMA.TABLES
                        WHERE TABLE_NAME = 'FieldVisitItineraries'
                    ) THEN 1 ELSE 0 END";

                var connection = context.Database.GetDbConnection();
                var wasOpen = connection.State == System.Data.ConnectionState.Open;

                if (!wasOpen)
                    await context.Database.OpenConnectionAsync();

                using var command = connection.CreateCommand();
                command.CommandText = tableExistsQuery;
                var result = await command.ExecuteScalarAsync();
                tableExists = Convert.ToInt32(result) == 1;

                if (!wasOpen)
                    await context.Database.CloseConnectionAsync();
            }
            catch
            {
                // إذا فشل الاستعلام، نفترض أن الجدول غير موجود
                tableExists = false;
            }

            if (tableExists)
            {
                System.Diagnostics.Debug.WriteLine("✅ جدول FieldVisitItineraries موجود مسبقاً - لن يتم إعادة إنشاؤه");
                return;
            }

            System.Diagnostics.Debug.WriteLine("🔧 إنشاء جدول FieldVisitItineraries...");

            // إنشاء الجدول الجديد
            var createTableSql = @"
                CREATE TABLE [FieldVisitItineraries] (
                    [Id] int IDENTITY(1,1) NOT NULL,
                    [FieldVisitId] int NOT NULL,
                    [DayNumber] int NOT NULL,
                    [ItineraryText] nvarchar(1000) NOT NULL,
                    [CreatedAt] datetime2 NOT NULL DEFAULT (GETDATE()),
                    [UpdatedAt] datetime2 NULL,
                    [Notes] nvarchar(500) NULL,
                    [IsActive] bit NOT NULL DEFAULT (1),
                    CONSTRAINT [PK_FieldVisitItineraries] PRIMARY KEY ([Id])
                );";

            await context.Database.ExecuteSqlRawAsync(createTableSql);
            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول FieldVisitItineraries");

            // إضافة Foreign Key إذا كان جدول FieldVisits موجود
            var addForeignKeySql = @"
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'FieldVisits')
                BEGIN
                    ALTER TABLE [FieldVisitItineraries]
                    ADD CONSTRAINT [FK_FieldVisitItineraries_FieldVisits]
                    FOREIGN KEY ([FieldVisitId]) REFERENCES [FieldVisits] ([Id]) ON DELETE CASCADE;
                END";

            await context.Database.ExecuteSqlRawAsync(addForeignKeySql);
            System.Diagnostics.Debug.WriteLine("🔗 تم إضافة Foreign Key");

            // إضافة الفهارس
            var addIndexesSql = @"
                CREATE INDEX [IX_FieldVisitItineraries_FieldVisitId] ON [FieldVisitItineraries] ([FieldVisitId]);
                CREATE INDEX [IX_FieldVisitItineraries_IsActive] ON [FieldVisitItineraries] ([IsActive]);
                CREATE UNIQUE INDEX [IX_FieldVisitItineraries_FieldVisitId_DayNumber] ON [FieldVisitItineraries] ([FieldVisitId], [DayNumber]);";

            await context.Database.ExecuteSqlRawAsync(addIndexesSql);
            System.Diagnostics.Debug.WriteLine("📊 تم إضافة الفهارس");

            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول FieldVisitItineraries بنجاح!");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول FieldVisitItineraries: {ex.Message}");
            // لا نرمي الخطأ لأن هذا قد يوقف تشغيل النظام
        }
    }

    /// <summary>
    /// تنظيف رسائل السائق من مهام النزول عند بدء التطبيق
    /// </summary>
    private async Task CleanDriverMessagesOnStartup()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🧹 بدء تنظيف رسائل السائق من مهام النزول...");

            var dataService = new DatabaseService();
            var result = await dataService.CleanAllDriverMessagesAsync();

            if (result)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم تنظيف رسائل السائق بنجاح");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على رسائل سائق للتنظيف");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف رسائل السائق: {ex.Message}");
            // لا نوقف التطبيق بسبب هذا الخطأ
        }
    }

}

