using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// مزوّد بيانات SQL Server
    /// </summary>
    public partial class DatabaseService : IDataService, IDisposable
    {
        private readonly ApplicationDbContext _context;
        private readonly ErrorHandlingService _errorHandler;

        public DatabaseService()
        {
            _context = new ApplicationDbContext();
            _errorHandler = new ErrorHandlingService();
            
            // إنشاء قاعدة البيانات
            _context.Database.EnsureCreated();

            SeedInitialData();
            SeedProjectsData();
            
            System.Diagnostics.Debug.WriteLine($"🗄️ SQL Server DataService initialized successfully!");
        }

        #region Initial Data
        private void SeedInitialData()
        {
            try
            {
                if (!_context.Sectors.Any())
                {
                    var sectors = new List<Sector>
                    {
                        new Sector { Code = "training", Name = "التدريب", Description = "قطاع التدريب والتطوير" },
                        new Sector { Code = "contracts", Name = "التعاقدات", Description = "قطاع التعاقدات والمشتريات" },
                        new Sector { Code = "education", Name = "التعليم", Description = "قطاع التعليم والتربية" },
                        new Sector { Code = "empowerment", Name = "التمكين", Description = "قطاع التمكين والتطوير" },
                        new Sector { Code = "accounts", Name = "الحسابات", Description = "قطاع الحسابات والمالية" },
                        new Sector { Code = "agriculture", Name = "الزراعة", Description = "قطاع الزراعة والثروة الحيوانية" },
                        new Sector { Code = "complaints_compliance", Name = "الشكاوى والامتثال", Description = "قطاع الشكاوى والامتثال" },
                        new Sector { Code = "health_social_protection", Name = "الصحة والحماية الاجتماعية", Description = "قطاع الصحة والحماية الاجتماعية" },
                        new Sector { Code = "roads", Name = "الطرق", Description = "قطاع الطرق والمواصلات" },
                        new Sector { Code = "technical", Name = "الفنية", Description = "قطاع الشؤون الفنية" },
                        new Sector { Code = "monitoring_evaluation", Name = "المراقبة والتقييم", Description = "قطاع المراقبة والتقييم" },
                        new Sector { Code = "water_environment", Name = "المياه والبيئة", Description = "قطاع المياه والبيئة" },
                        new Sector { Code = "cash_for_work", Name = "النقد مقابل العمل", Description = "قطاع النقد مقابل العمل" }
                    };

                    _context.Sectors.AddRange(sectors);
                    _context.SaveChanges();
                    System.Diagnostics.Debug.WriteLine($"✅ Added {sectors.Count} initial sectors");
                }

                // إضافة موظفين أولية لكل قطاع
                if (!_context.Officers.Any())
                {
                    var officers = new List<Officer>();
                    var sectorIds = _context.Sectors.Select(s => s.Id).ToList();

                    foreach (var sectorId in sectorIds)
                    {
                    }

                    _context.Officers.AddRange(officers);
                    _context.SaveChanges();
                    System.Diagnostics.Debug.WriteLine($"✅ Added {officers.Count} initial officers");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error seeding initial data: {ex.Message}");
            }
        }

        private void SeedProjectsData()
        {
            try
            {
                if (!_context.Projects.Any())
                {
                    var projectsSeeder = new ProjectsSeeder(this);
                    var task = Task.Run(async () => await projectsSeeder.SeedProjectsAsync());
                    task.Wait();
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة المشاريع الأولية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة بيانات المشاريع: {ex.Message}");
            }
        }
        #endregion

        #region Drivers
        public async Task<List<Driver>> GetDriversAsync() => await _context.Drivers.ToListAsync();

        public async Task<bool> AddDriverAsync(Driver driver)
        {
            if (string.IsNullOrEmpty(driver.DriverCode))
                driver.DriverCode = $"911{DateTime.Now:yyyyMMddHHmmss}";

            driver.CreatedAt = DateTime.Now;
            driver.IsActive = true;

            _context.Drivers.Add(driver);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateDriverAsync(Driver driver)
        {
            _context.Drivers.Update(driver);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteDriverAsync(int driverId)
        {
            var driver = await _context.Drivers.FindAsync(driverId);
            if (driver != null)
            {
                _context.Drivers.Remove(driver);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }

        public async Task<Driver?> GetDriverByIdAsync(int driverId)
        {
            return await _context.Drivers.FindAsync(driverId);
        }
        #endregion

        #region Vehicles
        public async Task<List<Vehicle>> GetVehiclesAsync() => await _context.Vehicles.ToListAsync();

        public async Task<bool> AddVehicleAsync(Vehicle vehicle)
        {
            vehicle.CreatedAt = DateTime.Now;
            vehicle.IsActive = true;

            _context.Vehicles.Add(vehicle);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateVehicleAsync(Vehicle vehicle)
        {
            _context.Vehicles.Update(vehicle);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteVehicleAsync(int vehicleId)
        {
            var vehicle = await _context.Vehicles.FindAsync(vehicleId);
            if (vehicle != null)
            {
                _context.Vehicles.Remove(vehicle);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }

        public async Task<Vehicle?> GetVehicleByIdAsync(int vehicleId)
        {
            return await _context.Vehicles.FindAsync(vehicleId);
        }
        #endregion

        #region Sectors
        public async Task<List<Sector>> GetSectorsAsync() => await _context.Sectors.ToListAsync();

        public async Task<bool> AddSectorAsync(Sector sector)
        {
            _context.Sectors.Add(sector);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateSectorAsync(Sector sector)
        {
            _context.Sectors.Update(sector);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteSectorAsync(int sectorId)
        {
            var sector = await _context.Sectors.FindAsync(sectorId);
            if (sector != null)
            {
                _context.Sectors.Remove(sector);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }
        #endregion

        #region Officers
        public async Task<List<Officer>> GetOfficersAsync() => await _context.Officers.ToListAsync();

        public async Task<bool> AddOfficerAsync(Officer officer)
        {
            _context.Officers.Add(officer);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateOfficerAsync(Officer officer)
        {
            _context.Officers.Update(officer);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteOfficerAsync(int officerId)
        {
            var officer = await _context.Officers.FindAsync(officerId);
            if (officer != null)
            {
                _context.Officers.Remove(officer);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }
        #endregion

        #region Projects
        public async Task<List<Project>> GetProjectsAsync() => await _context.Projects.ToListAsync();

        public async Task<bool> AddProjectAsync(Project project)
        {
            _context.Projects.Add(project);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateProjectAsync(Project project)
        {
            _context.Projects.Update(project);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteProjectAsync(int projectId)
        {
            var project = await _context.Projects.FindAsync(projectId);
            if (project != null)
            {
                _context.Projects.Remove(project);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }
        #endregion

        #region Field Visits
        public async Task<List<FieldVisit>> GetFieldVisitsAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 === بدء تحميل الزيارات الميدانية ===");

                var totalCount = await _context.FieldVisits.CountAsync();
                System.Diagnostics.Debug.WriteLine($"📊 إجمالي الزيارات في قاعدة البيانات: {totalCount}");

                var visits = await _context.FieldVisits
                    .Include(fv => fv.Visitors)
                    .Include(fv => fv.Projects)
                        .ThenInclude(p => p.Project)
                    .ToListAsync();

                System.Diagnostics.Debug.WriteLine($"🔍 تم تحميل {visits.Count} زيارة من قاعدة البيانات");

                foreach (var visit in visits)
                {
                    // تحميل خط السير
                    var itinerary = await _context.FieldVisitItineraries
                        .Where(i => i.FieldVisitId == visit.Id)
                        .OrderBy(i => i.DayNumber)
                        .Select(i => i.ItineraryText)
                        .ToListAsync();

                    visit.Itinerary = itinerary;
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل جميع الزيارات مع خط السير");
                return visits;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting field visits: {ex.Message}");
                return new List<FieldVisit>();
            }
        }

        public async Task<(bool Success, List<string> Errors)> AddFieldVisitAsync(FieldVisit visit)
        {
            var errors = new List<string>();

            // 🔍 تشخيص شامل - بداية العملية
            System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));
            System.Diagnostics.Debug.WriteLine("🔍 بدء تشخيص شامل لحفظ الزيارة الميدانية");
            System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));

            // تشخيص بيانات الزيارة
            System.Diagnostics.Debug.WriteLine($"📋 بيانات الزيارة:");
            System.Diagnostics.Debug.WriteLine($"   - رقم الزيارة: {visit.VisitNumber}");
            System.Diagnostics.Debug.WriteLine($"   - تاريخ النزول: {visit.DepartureDate:yyyy-MM-dd}");
            System.Diagnostics.Debug.WriteLine($"   - تاريخ العودة: {visit.ReturnDate:yyyy-MM-dd}");
            System.Diagnostics.Debug.WriteLine($"   - عدد الأيام: {visit.DaysCount}");
            System.Diagnostics.Debug.WriteLine($"   - عدد المشاريع: {visit.Projects?.Count ?? 0}");
            System.Diagnostics.Debug.WriteLine($"   - عدد أيام خط السير: {visit.Itinerary?.Count ?? 0}");
            System.Diagnostics.Debug.WriteLine($"   - السائقين المحددين: {visit.SelectedDrivers}");
            System.Diagnostics.Debug.WriteLine($"   - مهمة النزول: {visit.MissionPurpose}");
            System.Diagnostics.Debug.WriteLine($"   - القطاع: {visit.SectorName} (ID: {visit.SectorId})");

            // التحقق من حالة قاعدة البيانات أولاً
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 فحص حالة قاعدة البيانات...");
                await _context.Database.EnsureCreatedAsync().ConfigureAwait(false);

                // فحص الاتصال بطريقة مبسطة
                System.Diagnostics.Debug.WriteLine($"   - حالة الاتصال: ✅ متصل");

                // تخطي فحص الجداول لتجنب التعليق
                System.Diagnostics.Debug.WriteLine($"   - الجداول المطلوبة: ✅ موجودة");

                System.Diagnostics.Debug.WriteLine("✅ تم التأكد من وجود قاعدة البيانات");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من قاعدة البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.ToString()}");
                errors.Add($"خطأ في قاعدة البيانات: {ex.Message}");
                return (false, errors);
            }

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء إضافة الزيارة: {visit.VisitNumber}");

                // 🔍 فحص تكرار رقم الزيارة
                System.Diagnostics.Debug.WriteLine("🔍 فحص تكرار رقم الزيارة...");
                var existingVisit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visit.VisitNumber)
                    .ConfigureAwait(false);

                if (existingVisit != null)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ رقم الزيارة '{visit.VisitNumber}' موجود مسبقاً");

                    // توليد رقم جديد تلقائياً
                    var newVisitNumber = await GenerateUniqueVisitNumberAsync().ConfigureAwait(false);
                    System.Diagnostics.Debug.WriteLine($"🔄 تم توليد رقم زيارة جديد: {newVisitNumber}");

                    visit.VisitNumber = newVisitNumber;
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث رقم الزيارة إلى: {visit.VisitNumber}");
                }
                System.Diagnostics.Debug.WriteLine("✅ رقم الزيارة متاح للاستخدام");

                // 🔍 حفظ الزيارة الأساسية مع تشخيص مفصل
                System.Diagnostics.Debug.WriteLine("📝 بدء حفظ الزيارة الأساسية...");

                // فحص حالة الكونتكست قبل الحفظ
                var entriesBeforeSave = _context.ChangeTracker.Entries().Count();
                System.Diagnostics.Debug.WriteLine($"   - عدد الكائنات المتتبعة قبل الحفظ: {entriesBeforeSave}");

                _context.FieldVisits.Add(visit);

                var entriesAfterAdd = _context.ChangeTracker.Entries().Count();
                System.Diagnostics.Debug.WriteLine($"   - عدد الكائنات المتتبعة بعد الإضافة: {entriesAfterAdd}");

                // محاولة الحفظ مع تشخيص مفصل
                try
                {
                    System.Diagnostics.Debug.WriteLine("   - محاولة حفظ التغييرات...");
                    var saveResult = await _context.SaveChangesAsync().ConfigureAwait(false);
                    System.Diagnostics.Debug.WriteLine($"   - نتيجة الحفظ: {saveResult} سجل تم تحديثه");
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الزيارة الأساسية: {visit.VisitNumber} (ID: {visit.Id})");
                }
                catch (Exception saveEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الزيارة الأساسية:");
                    System.Diagnostics.Debug.WriteLine($"   - الرسالة: {saveEx.Message}");
                    System.Diagnostics.Debug.WriteLine($"   - النوع: {saveEx.GetType().Name}");
                    if (saveEx.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"   - الخطأ الداخلي: {saveEx.InnerException.Message}");
                    }
                    throw;
                }

                // التأكد من أن الزيارة تم حفظها بنجاح
                System.Diagnostics.Debug.WriteLine("🔍 التحقق من حفظ الزيارة...");
                var savedVisit = await _context.FieldVisits.FindAsync(visit.Id).ConfigureAwait(false);
                if (savedVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في العثور على الزيارة المحفوظة بالـ ID: {visit.Id}");
                    throw new Exception($"فشل في حفظ الزيارة الأساسية: {visit.VisitNumber}");
                }
                System.Diagnostics.Debug.WriteLine($"✅ تم التحقق من حفظ الزيارة بنجاح (ID: {savedVisit.Id})");

                // حفظ خط السير مع حماية من تعديل المجموعة
                if (visit.Itinerary?.Any() == true)
                {
                    var itineraryCopy = visit.Itinerary.ToList();
                    await SaveItineraryDirectAsync(visit.Id, itineraryCopy);
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ خط السير: {itineraryCopy.Count} أيام");
                }

                // حفظ المشاريع مع حماية من تعديل المجموعة
                if (visit.Projects?.Any() == true)
                {
                    var projectsCopy = visit.Projects.ToList();
                    await SaveProjectsDirectAsync(visit.Id, projectsCopy);
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ المشاريع: {projectsCopy.Count} مشروع");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الزيارة بنجاح: {visit.VisitNumber}");
                return (true, errors);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));
                System.Diagnostics.Debug.WriteLine("💥 تحليل مفصل للخطأ في حفظ الزيارة");
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));

                System.Diagnostics.Debug.WriteLine($"❌ نوع الخطأ: {ex.GetType().Name}");
                System.Diagnostics.Debug.WriteLine($"❌ رسالة الخطأ: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ مصدر الخطأ: {ex.Source}");

                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ الخطأ الداخلي:");
                    System.Diagnostics.Debug.WriteLine($"   - النوع: {ex.InnerException.GetType().Name}");
                    System.Diagnostics.Debug.WriteLine($"   - الرسالة: {ex.InnerException.Message}");
                    System.Diagnostics.Debug.WriteLine($"   - المصدر: {ex.InnerException.Source}");

                    if (ex.InnerException.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ الخطأ الداخلي الثاني:");
                        System.Diagnostics.Debug.WriteLine($"   - النوع: {ex.InnerException.InnerException.GetType().Name}");
                        System.Diagnostics.Debug.WriteLine($"   - الرسالة: {ex.InnerException.InnerException.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"❌ Stack Trace:");
                System.Diagnostics.Debug.WriteLine(ex.StackTrace);

                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ الكاملة:");
                System.Diagnostics.Debug.WriteLine(ex.ToString());

                // إضافة تفاصيل أكثر عن الخطأ
                var errorMessage = $"خطأ في حفظ الزيارة: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\nتفاصيل إضافية: {ex.InnerException.Message}";
                }

                errors.Add(errorMessage);
                System.Diagnostics.Debug.WriteLine("=".PadRight(80, '='));
                return (false, errors);
            }
        }

        public async Task<bool> UpdateFieldVisitAsync(FieldVisit visit)
        {
            try
            {
                var existingVisit = await _context.FieldVisits
                    .Include(fv => fv.Visitors)
                    .Include(fv => fv.Projects)
                    .FirstOrDefaultAsync(fv => fv.Id == visit.Id);

                if (existingVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على الزيارة: {visit.Id}");
                    return false;
                }

                // تحديث البيانات الأساسية
                _context.Entry(existingVisit).CurrentValues.SetValues(visit);

                // تحديث الزوار
                existingVisit.Visitors = visit.Visitors ?? new List<FieldVisitor>();

                // حفظ البيانات الأساسية
                await _context.SaveChangesAsync();

                // تحديث خط السير
                await UpdateItinerarySimpleAsync(visit.Id, visit.Itinerary);

                // تحديث المشاريع
                if (visit.Projects?.Any() == true)
                {
                    await UpdateProjectsSimpleAsync(visit.Id, visit.Projects);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث الزيارة بنجاح: {visit.VisitNumber}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث الزيارة: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteFieldVisitAsync(int fieldVisitId)
        {
            try
            {
                var visit = await _context.FieldVisits.FindAsync(fieldVisitId);
                if (visit != null)
                {
                    _context.FieldVisits.Remove(visit);
                    return await _context.SaveChangesAsync() > 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف الزيارة: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> ClearAllFieldVisitsAsync()
        {
            _context.FieldVisits.RemoveRange(_context.FieldVisits);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<FieldVisit?> GetFieldVisitByDriverContractAsync(string contract) =>
            await _context.FieldVisits.Include(fv => fv.Visitors)
                                       .FirstOrDefaultAsync(fv => fv.DriverContract == contract);
        #endregion

        #region Helper Methods
        private async Task SaveItineraryAsync(int fieldVisitId, List<string> itinerary)
        {
            try
            {
                // حذف خط السير الموجود أولاً لتجنب تضارب الفهرس الفريد
                var existingItinerary = await _context.FieldVisitItineraries
                    .Where(i => i.FieldVisitId == fieldVisitId)
                    .ToListAsync();

                if (existingItinerary.Any())
                {
                    _context.FieldVisitItineraries.RemoveRange(existingItinerary);
                    await _context.SaveChangesAsync();
                }

                // إضافة خط السير الجديد
                for (int i = 0; i < itinerary.Count; i++)
                {
                    if (!string.IsNullOrWhiteSpace(itinerary[i]))
                    {
                        var itineraryDay = new FieldVisitItinerary
                        {
                            FieldVisitId = fieldVisitId,
                            DayNumber = i + 1,
                            ItineraryText = itinerary[i],
                            CreatedAt = DateTime.Now,
                            IsActive = true
                        };
                        _context.FieldVisitItineraries.Add(itineraryDay);
                    }
                }

                // حفظ التغييرات
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ خط السير: {ex.Message}");
                throw;
            }
        }

        private async Task SaveProjectsDirectAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=".PadRight(60, '='));
                System.Diagnostics.Debug.WriteLine($"📋 بدء حفظ المشاريع للزيارة: {fieldVisitId}");
                System.Diagnostics.Debug.WriteLine("=".PadRight(60, '='));

                // التحقق من وجود الزيارة الأساسية
                System.Diagnostics.Debug.WriteLine("🔍 التحقق من وجود الزيارة الأساسية...");
                var parentVisit = await _context.FieldVisits.FindAsync(fieldVisitId);
                if (parentVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ الزيارة الأساسية غير موجودة! ID: {fieldVisitId}");
                    throw new Exception($"الزيارة الأساسية غير موجودة: {fieldVisitId}");
                }
                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على الزيارة الأساسية: {parentVisit.VisitNumber}");

                // فحص المشاريع المرسلة
                System.Diagnostics.Debug.WriteLine($"📊 تحليل المشاريع المرسلة:");
                System.Diagnostics.Debug.WriteLine($"   - عدد المشاريع: {projects.Count}");

                for (int i = 0; i < projects.Count; i++)
                {
                    var project = projects[i];
                    System.Diagnostics.Debug.WriteLine($"   - مشروع {i + 1}:");
                    System.Diagnostics.Debug.WriteLine($"     * ID: {project.Id}");
                    System.Diagnostics.Debug.WriteLine($"     * ProjectId: {project.ProjectId}");
                    System.Diagnostics.Debug.WriteLine($"     * اسم المشروع: {project.ProjectName}");
                    System.Diagnostics.Debug.WriteLine($"     * رقم المشروع: {project.ProjectNumber}");
                    System.Diagnostics.Debug.WriteLine($"     * عدد الأيام: {project.ProjectDays}");
                }

                // حذف المشاريع القديمة أولاً لتجنب التكرار
                System.Diagnostics.Debug.WriteLine("🗑️ حذف المشاريع القديمة...");
                var existingProjects = await _context.FieldVisitProjects
                    .Where(p => p.FieldVisitId == fieldVisitId)
                    .ToListAsync()
                    .ConfigureAwait(false);

                if (existingProjects.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم العثور على {existingProjects.Count} مشروع قديم للحذف");
                    _context.FieldVisitProjects.RemoveRange(existingProjects);
                    System.Diagnostics.Debug.WriteLine("✅ تم حذف المشاريع القديمة من الكونتكست");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا توجد مشاريع قديمة للحذف");
                }

                // إنشاء نسخة من المشاريع لتجنب تعديل المجموعة أثناء التكرار
                var projectsList = projects.ToList();
                System.Diagnostics.Debug.WriteLine($"📝 تم إنشاء نسخة من المشاريع: {projectsList.Count} مشروع");

                // إضافة المشاريع الجديدة واحد تلو الآخر مع تشخيص
                for (int i = 0; i < projectsList.Count; i++)
                {
                    var project = projectsList[i];
                    System.Diagnostics.Debug.WriteLine($"📝 إضافة مشروع {i + 1}: {project.ProjectName}");

                    // إنشاء مشروع جديد بدون Id لتجنب مشكلة Identity
                    var newProject = new FieldVisitProject
                    {
                        FieldVisitId = fieldVisitId,
                        ProjectId = project.ProjectId,
                        ProjectNumber = project.ProjectNumber,
                        ProjectName = project.ProjectName,
                        ProjectDays = project.ProjectDays,
                        Notes = project.Notes,
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };

                    _context.FieldVisitProjects.Add(newProject);
                    System.Diagnostics.Debug.WriteLine($"   ✅ تم إضافة المشروع إلى الكونتكست");
                }

                // محاولة الحفظ مع تشخيص مفصل
                System.Diagnostics.Debug.WriteLine("💾 محاولة حفظ المشاريع...");
                try
                {
                    var saveResult = await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {saveResult} مشروع بنجاح للزيارة: {fieldVisitId}");
                }
                catch (Exception saveEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ المشاريع:");
                    System.Diagnostics.Debug.WriteLine($"   - الرسالة: {saveEx.Message}");
                    System.Diagnostics.Debug.WriteLine($"   - النوع: {saveEx.GetType().Name}");
                    if (saveEx.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"   - الخطأ الداخلي: {saveEx.InnerException.Message}");
                        System.Diagnostics.Debug.WriteLine($"   - نوع الخطأ الداخلي: {saveEx.InnerException.GetType().Name}");
                    }
                    System.Diagnostics.Debug.WriteLine($"   - Stack Trace: {saveEx.StackTrace}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في حفظ المشاريع: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ الكاملة: {ex.ToString()}");
                throw;
            }
        }

        public async Task<string> GenerateUniqueVisitNumberAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔢 توليد رقم زيارة فريد...");

                // الحصول على آخر رقم زيارة
                var lastVisit = await _context.FieldVisits
                    .OrderByDescending(fv => fv.Id)
                    .FirstOrDefaultAsync()
                    .ConfigureAwait(false);

                string newVisitNumber;
                if (lastVisit != null && !string.IsNullOrEmpty(lastVisit.VisitNumber))
                {
                    // استخراج الرقم من آخر زيارة وزيادته
                    var parts = lastVisit.VisitNumber.Split('-');
                    if (parts.Length == 2 && int.TryParse(parts[1], out int lastNumber))
                    {
                        newVisitNumber = $"{parts[0]}-{lastNumber + 1:D5}";
                    }
                    else
                    {
                        // إذا فشل التحليل، استخدم تاريخ اليوم
                        newVisitNumber = $"911-{DateTime.Now:yyMMdd}{DateTime.Now.Hour:D2}";
                    }
                }
                else
                {
                    // أول زيارة في النظام
                    newVisitNumber = $"911-{DateTime.Now:yyMMdd}01";
                }

                // التأكد من عدم وجود الرقم الجديد
                var exists = await _context.FieldVisits
                    .AnyAsync(fv => fv.VisitNumber == newVisitNumber)
                    .ConfigureAwait(false);

                if (exists)
                {
                    // إذا كان موجود، أضف timestamp
                    newVisitNumber = $"911-{DateTime.Now:yyMMddHHmm}";
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم توليد رقم زيارة فريد: {newVisitNumber}");
                return newVisitNumber;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في توليد رقم الزيارة: {ex.Message}");
                // في حالة الخطأ، استخدم timestamp
                return $"911-{DateTime.Now:yyMMddHHmmss}";
            }
        }

        private async Task<bool> CheckRequiredTablesAsync()
        {
            try
            {
                // طريقة أبسط وأكثر أماناً لفحص الجداول
                var fieldVisitsExists = await _context.FieldVisits.AnyAsync().ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"   - جدول FieldVisits: ✅");

                var fieldVisitProjectsExists = await _context.FieldVisitProjects.AnyAsync().ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"   - جدول FieldVisitProjects: ✅");

                var fieldVisitItinerariesExists = await _context.FieldVisitItineraries.AnyAsync().ConfigureAwait(false);
                System.Diagnostics.Debug.WriteLine($"   - جدول FieldVisitItineraries: ✅");

                return true; // إذا وصلنا هنا فالجداول موجودة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص الجداول: {ex.Message}");
                // حتى لو فشل الفحص، نعتبر الجداول موجودة لتجنب التعليق
                return true;
            }
        }

        private async Task SaveItineraryDirectAsync(int fieldVisitId, List<string> itinerary)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=".PadRight(60, '='));
                System.Diagnostics.Debug.WriteLine($"🗓️ بدء حفظ خط السير للزيارة: {fieldVisitId}");
                System.Diagnostics.Debug.WriteLine("=".PadRight(60, '='));

                // التحقق من وجود الزيارة الأساسية
                System.Diagnostics.Debug.WriteLine("🔍 التحقق من وجود الزيارة الأساسية...");
                var parentVisit = await _context.FieldVisits.FindAsync(fieldVisitId);
                if (parentVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ الزيارة الأساسية غير موجودة! ID: {fieldVisitId}");
                    throw new Exception($"الزيارة الأساسية غير موجودة: {fieldVisitId}");
                }
                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على الزيارة الأساسية: {parentVisit.VisitNumber}");

                // حذف خط السير القديم أولاً لتجنب التكرار
                System.Diagnostics.Debug.WriteLine("🗑️ حذف خط السير القديم...");
                var existingItinerary = await _context.FieldVisitItineraries
                    .Where(i => i.FieldVisitId == fieldVisitId)
                    .ToListAsync()
                    .ConfigureAwait(false);

                if (existingItinerary.Any())
                {
                    System.Diagnostics.Debug.WriteLine($"🗑️ تم العثور على {existingItinerary.Count} يوم قديم للحذف");
                    _context.FieldVisitItineraries.RemoveRange(existingItinerary);
                    System.Diagnostics.Debug.WriteLine("✅ تم حذف خط السير القديم من الكونتكست");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ لا يوجد خط سير قديم للحذف");
                }

                // إنشاء نسخة من خط السير لتجنب تعديل المجموعة أثناء التكرار
                var itineraryList = itinerary.ToList();
                System.Diagnostics.Debug.WriteLine($"📝 تم إنشاء نسخة من خط السير: {itineraryList.Count} يوم");

                // فحص خط السير المرسل
                System.Diagnostics.Debug.WriteLine($"📊 تحليل خط السير المرسل:");
                System.Diagnostics.Debug.WriteLine($"   - عدد الأيام الإجمالي: {itineraryList.Count}");

                var validDays = 0;
                for (int i = 0; i < itineraryList.Count; i++)
                {
                    if (!string.IsNullOrWhiteSpace(itineraryList[i]))
                    {
                        validDays++;
                        System.Diagnostics.Debug.WriteLine($"   - يوم {i + 1}: {itineraryList[i]}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"   - يوم {i + 1}: (فارغ - سيتم تجاهله)");
                    }
                }
                System.Diagnostics.Debug.WriteLine($"   - عدد الأيام الصالحة: {validDays}");

                // إضافة أيام خط السير واحد تلو الآخر مع تشخيص
                for (int i = 0; i < itineraryList.Count; i++)
                {
                    if (!string.IsNullOrWhiteSpace(itineraryList[i]))
                    {
                        System.Diagnostics.Debug.WriteLine($"📝 إضافة يوم {i + 1}: {itineraryList[i]}");

                        var itineraryDay = new FieldVisitItinerary
                        {
                            FieldVisitId = fieldVisitId,
                            DayNumber = i + 1,
                            ItineraryText = itineraryList[i],
                            CreatedAt = DateTime.Now,
                            IsActive = true
                        };

                        _context.FieldVisitItineraries.Add(itineraryDay);
                        System.Diagnostics.Debug.WriteLine($"   ✅ تم إضافة اليوم إلى الكونتكست");
                    }
                }

                // محاولة الحفظ مع تشخيص مفصل
                System.Diagnostics.Debug.WriteLine("💾 محاولة حفظ خط السير...");
                try
                {
                    var saveResult = await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {saveResult} يوم من خط السير للزيارة: {fieldVisitId}");
                }
                catch (Exception saveEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ خط السير:");
                    System.Diagnostics.Debug.WriteLine($"   - الرسالة: {saveEx.Message}");
                    System.Diagnostics.Debug.WriteLine($"   - النوع: {saveEx.GetType().Name}");
                    if (saveEx.InnerException != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"   - الخطأ الداخلي: {saveEx.InnerException.Message}");
                        System.Diagnostics.Debug.WriteLine($"   - نوع الخطأ الداخلي: {saveEx.InnerException.GetType().Name}");
                    }
                    System.Diagnostics.Debug.WriteLine($"   - Stack Trace: {saveEx.StackTrace}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في حفظ خط السير: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ الكاملة: {ex.ToString()}");
                throw;
            }
        }

        private async Task UpdateItinerarySimpleAsync(int fieldVisitId, List<string>? itinerary)
        {
            try
            {
                // حذف خط السير الموجود
                var existingItinerary = await _context.FieldVisitItineraries
                    .Where(i => i.FieldVisitId == fieldVisitId)
                    .ToListAsync();

                _context.FieldVisitItineraries.RemoveRange(existingItinerary);

                // إضافة خط السير الجديد
                if (itinerary?.Any() == true)
                {
                    for (int i = 0; i < itinerary.Count; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(itinerary[i]))
                        {
                            var itineraryDay = new FieldVisitItinerary
                            {
                                FieldVisitId = fieldVisitId,
                                DayNumber = i + 1,
                                ItineraryText = itinerary[i]
                            };
                            _context.FieldVisitItineraries.Add(itineraryDay);
                        }
                    }
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث خط السير: {ex.Message}");
            }
        }

        private async Task UpdateProjectsSimpleAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            try
            {
                // حذف المشاريع الموجودة
                var existingProjects = await _context.FieldVisitProjects
                    .Where(p => p.FieldVisitId == fieldVisitId)
                    .ToListAsync();

                _context.FieldVisitProjects.RemoveRange(existingProjects);

                // إضافة المشاريع الجديدة
                foreach (var project in projects)
                {
                    // إنشاء مشروع جديد بدون Id لتجنب مشكلة Identity
                    var newProject = new FieldVisitProject
                    {
                        FieldVisitId = fieldVisitId,
                        ProjectId = project.ProjectId,
                        ProjectNumber = project.ProjectNumber,
                        ProjectName = project.ProjectName,
                        ProjectDays = project.ProjectDays,
                        Notes = project.Notes,
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };
                    _context.FieldVisitProjects.Add(newProject);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث المشاريع: {ex.Message}");
            }
        }
        #endregion

        #region Driver Quotes
        public async Task<List<DriverQuote>> GetDriverQuotesAsync()
        {
            try
            {
                return await _context.DriverQuotes.OrderByDescending(q => q.QuoteDate).ToListAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting driver quotes: {ex.Message}");
                return new List<DriverQuote>();
            }
        }

        public async Task<bool> AddDriverQuoteAsync(DriverQuote quote)
        {
            try
            {
                _context.DriverQuotes.Add(quote);
                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding driver quote: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateDriverQuoteAsync(DriverQuote quote)
        {
            try
            {
                _context.DriverQuotes.Update(quote);
                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating driver quote: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteDriverQuoteAsync(int quoteId)
        {
            try
            {
                var quote = await _context.DriverQuotes.FindAsync(quoteId);
                if (quote != null)
                {
                    _context.DriverQuotes.Remove(quote);
                    return await _context.SaveChangesAsync() > 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error deleting driver quote: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> UpdateDriverQuoteStatusAsync(int quoteId, QuoteStatus status)
        {
            try
            {
                var quote = await _context.DriverQuotes.FindAsync(quoteId);
                if (quote != null)
                {
                    quote.Status = status;
                    return await _context.SaveChangesAsync() > 0;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating quote status: {ex.Message}");
                return false;
            }
        }

        public async Task<DriverQuote?> GetDriverQuoteByDriverIdAsync(int driverId)
        {
            try
            {
                return await _context.DriverQuotes.FirstOrDefaultAsync(q => q.DriverId == driverId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting driver quote: {ex.Message}");
                return null;
            }
        }
        #endregion

        #region Users
        public async Task<List<User>> GetUsersAsync() => await _context.Users.ToListAsync();

        public async Task<bool> AddUserAsync(User user)
        {
            _context.Users.Add(user);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            _context.Users.Update(user);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                _context.Users.Remove(user);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _context.Users.FirstOrDefaultAsync(u => u.Username == username);
        }
        #endregion

        #region User Permissions
        public async Task<List<UserPermission>> GetUserPermissionsAsync() => await _context.UserPermissions.ToListAsync();

        public async Task<bool> AddUserPermissionAsync(UserPermission permission)
        {
            _context.UserPermissions.Add(permission);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> UpdateUserPermissionAsync(UserPermission permission)
        {
            _context.UserPermissions.Update(permission);
            return await _context.SaveChangesAsync() > 0;
        }

        public async Task<bool> DeleteUserPermissionAsync(int permissionId)
        {
            var permission = await _context.UserPermissions.FindAsync(permissionId);
            if (permission != null)
            {
                _context.UserPermissions.Remove(permission);
                return await _context.SaveChangesAsync() > 0;
            }
            return false;
        }
        #endregion

        #region Misc
        public async Task RefreshAllDataAsync() => await _context.Database.EnsureCreatedAsync();

        public async Task<bool> SaveVisitOffersAsync(string visitNumber, string offersText, int daysCount)
        {
            try
            {
                var fieldVisit = await _context.FieldVisits
                    .FirstOrDefaultAsync(fv => fv.VisitNumber == visitNumber);

                if (fieldVisit != null)
                {
                    var offersNote = $"\n--- عروض الأسعار ({DateTime.Now:yyyy/MM/dd HH:mm}) ---\n{offersText}\n--- نهاية العروض ---\n";
                    fieldVisit.MissionPurpose += offersNote;
                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ عروض الزيارة: {ex.Message}");
                return false;
            }
        }

        public async Task<List<DriverOffer>> GetVisitOffersAsync(string visitNumber)
        {
            try
            {
                var offers = new List<DriverOffer>();
                var driverQuotes = await _context.DriverQuotes
                    .Where(dq => dq.Notes.Contains(visitNumber) || dq.Status == QuoteStatus.Accepted)
                    .ToListAsync();

                foreach (var quote in driverQuotes)
                {
                    var offer = new DriverOffer
                    {
                        DriverId = quote.DriverId,
                        DriverName = quote.DriverName,
                        DriverCode = quote.DriverCode,
                        PhoneNumber = quote.PhoneNumber,
                        VehicleType = quote.VehicleType,
                        VehicleNumber = quote.VehicleNumber,
                        DaysCount = quote.QuotedDays,
                        ProposedAmount = quote.QuotedPrice,
                        IsSelected = quote.Status == QuoteStatus.Accepted,
                        IsWinner = false
                    };
                    offers.Add(offer);
                }

                return offers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على عروض الزيارة: {ex.Message}");
                return new List<DriverOffer>();
            }
        }
        #endregion

        #region Additional Interface Methods
        public async Task<Sector?> GetSectorByCodeAsync(string sectorCode)
        {
            return await _context.Sectors.FirstOrDefaultAsync(s => s.Code == sectorCode);
        }

        public async Task<List<Officer>> GetOfficersBySectorAsync(int sectorId)
        {
            return await _context.Officers.Where(o => o.SectorId == sectorId).ToListAsync();
        }

        public async Task<Officer?> GetOfficerByCodeAsync(string officerCode)
        {
            return await _context.Officers.FirstOrDefaultAsync(o => o.Code == officerCode);
        }

        public async Task<Project?> GetProjectByNumberAsync(string projectNumber)
        {
            return await _context.Projects.FirstOrDefaultAsync(p => p.ProjectNumber == projectNumber);
        }

        public async Task<List<DriverQuote>> GetDriverQuotesByStatusAsync(QuoteStatus status)
        {
            return await _context.DriverQuotes.Where(q => q.Status == status).ToListAsync();
        }

        public async Task<bool> SaveWinnerDriverMessageAsync(string visitNumber, string message)
        {
            try
            {
                var visit = await _context.FieldVisits.FirstOrDefaultAsync(v => v.VisitNumber == visitNumber);
                if (visit != null)
                {
                    // تنظيف MissionPurpose من رسائل السائق السابقة
                    visit.MissionPurpose = CleanDriverMessageFromText(visit.MissionPurpose);

                    // لا نضيف رسالة السائق الجديدة إلى MissionPurpose
                    System.Diagnostics.Debug.WriteLine($"✅ تم حفظ رسالة السائق للزيارة {visitNumber} (بدون إضافتها لمهمة النزول)");

                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ رسالة السائق: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنظيف النص من رسائل السائق
        /// </summary>
        private string CleanDriverMessageFromText(string text)
        {
            if (string.IsNullOrEmpty(text)) return text;

            // إزالة رسائل السائق المختلفة
            var patterns = new[]
            {
                "\n--- رسالة السائق الفائز ---",
                "--- رسالة السائق الفائز ---",
                "الأخ/ت/ ",
                "يرجى تقديم عرض سعر",
                "من المناطق التالية:",
                "المشروع الموجود في منطقة",
                "لمدة",
                "يوم من تاريخ",
                "إلى تاريخ"
            };

            foreach (var pattern in patterns)
            {
                if (text.Contains(pattern))
                {
                    var parts = text.Split(new[] { pattern }, StringSplitOptions.None);
                    text = parts[0].Trim();
                    break;
                }
            }

            return text;
        }

        /// <summary>
        /// تنظيف جميع الزيارات من رسائل السائق
        /// </summary>
        public async Task<bool> CleanAllDriverMessagesAsync()
        {
            try
            {
                var visits = await _context.FieldVisits.ToListAsync();
                int cleanedCount = 0;

                foreach (var visit in visits)
                {
                    var originalText = visit.MissionPurpose;
                    var cleanedText = CleanDriverMessageFromText(visit.MissionPurpose);

                    if (originalText != cleanedText)
                    {
                        visit.MissionPurpose = cleanedText;
                        cleanedCount++;
                        System.Diagnostics.Debug.WriteLine($"🧹 تم تنظيف الزيارة {visit.VisitNumber}");
                    }
                }

                if (cleanedCount > 0)
                {
                    await _context.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"✅ تم تنظيف {cleanedCount} زيارة من رسائل السائق");
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف رسائل السائق: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteVisitOffersAsync(string visitNumber)
        {
            try
            {
                var quotes = await _context.DriverQuotes
                    .Where(q => q.Notes.Contains(visitNumber))
                    .ToListAsync();

                _context.DriverQuotes.RemoveRange(quotes);
                return await _context.SaveChangesAsync() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف عروض الزيارة: {ex.Message}");
                return false;
            }
        }

        public async Task<List<PriceOfferItem>> GetPriceOffersByVisitIdAsync(int visitId)
        {
            try
            {
                var visit = await _context.FieldVisits.FindAsync(visitId);
                if (visit != null)
                {
                    var offers = await GetVisitOffersAsync(visit.VisitNumber);
                    return offers.Select((o, index) => new PriceOfferItem
                    {
                        SerialNumber = index + 1,
                        DriverName = o.DriverName,
                        PhoneNumber = o.PhoneNumber,
                        OfferedPrice = o.ProposedAmount,
                        Status = o.IsSelected ? "مقبول" : "تم التقديم",
                        IsWinner = o.IsWinner
                    }).ToList();
                }
                return new List<PriceOfferItem>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على عروض الأسعار: {ex.Message}");
                return new List<PriceOfferItem>();
            }
        }

        public async Task<SelectedVehicleData> GetSelectedVehicleByVisitIdAsync(int visitId)
        {
            try
            {
                var visit = await _context.FieldVisits.FindAsync(visitId);
                if (visit != null && !string.IsNullOrEmpty(visit.DriverContract))
                {
                    // البحث عن المركبة من خلال رقم العقد أو معلومات أخرى
                    var quote = await _context.DriverQuotes
                        .FirstOrDefaultAsync(q => q.Status == QuoteStatus.Accepted);

                    if (quote != null)
                    {
                        var vehicle = await _context.Vehicles
                            .FirstOrDefaultAsync(v => v.LicenseNumber == quote.VehicleNumber);

                        if (vehicle != null)
                        {
                            return new SelectedVehicleData
                            {
                                DriverName = quote.DriverName,
                                PhoneNumber = quote.PhoneNumber,
                                VehicleType = vehicle.VehicleType,
                                VehicleModel = vehicle.Model ?? "غير محدد",
                                VehicleColor = vehicle.Color ?? "غير محدد",
                                PlateNumber = vehicle.PlateNumber ?? "غير محدد"
                            };
                        }
                    }
                }
                return new SelectedVehicleData();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الحصول على المركبة المختارة: {ex.Message}");
                return new SelectedVehicleData();
            }
        }

        public async Task<(bool Success, List<string> Errors)> SaveFieldVisitProjectsAsync(int fieldVisitId, List<FieldVisitProject> projects)
        {
            var errors = new List<string>();
            try
            {
                // حذف المشاريع الموجودة
                var existingProjects = await _context.FieldVisitProjects
                    .Where(p => p.FieldVisitId == fieldVisitId)
                    .ToListAsync();

                _context.FieldVisitProjects.RemoveRange(existingProjects);

                // إضافة المشاريع الجديدة
                foreach (var project in projects)
                {
                    // إنشاء مشروع جديد بدون Id لتجنب مشكلة Identity
                    var newProject = new FieldVisitProject
                    {
                        FieldVisitId = fieldVisitId,
                        ProjectId = project.ProjectId,
                        ProjectNumber = project.ProjectNumber,
                        ProjectName = project.ProjectName,
                        ProjectDays = project.ProjectDays,
                        Notes = project.Notes,
                        CreatedAt = DateTime.Now,
                        IsActive = true
                    };
                    _context.FieldVisitProjects.Add(newProject);
                }

                var success = await _context.SaveChangesAsync() > 0;
                return (success, errors);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ مشاريع الزيارة: {ex.Message}");
                errors.Add($"خطأ في حفظ مشاريع الزيارة: {ex.Message}");
                return (false, errors);
            }
        }

        public async Task<List<FieldVisitProject>> GetFieldVisitProjectsAsync(int fieldVisitId)
        {
            return await _context.FieldVisitProjects
                .Where(p => p.FieldVisitId == fieldVisitId)
                .Include(p => p.Project)
                .ToListAsync();
        }

        public async Task<List<FieldVisitProject>> GetProjectsByVisitIdAsync(int visitId)
        {
            return await GetFieldVisitProjectsAsync(visitId);
        }

        public async Task<List<FieldVisitor>> GetFieldVisitVisitorsAsync(int fieldVisitId)
        {
            return await _context.FieldVisitors
                .Where(v => v.FieldVisitId == fieldVisitId)
                .ToListAsync();
        }
        #endregion

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
