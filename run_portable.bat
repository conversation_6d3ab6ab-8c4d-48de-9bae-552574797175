@echo off
echo ========================================
echo    🚀 نظام إدارة الزيارات الميدانية المحمول
echo    📊 SQL Server مع إعدادات محمولة
echo ========================================
echo.

echo 📦 جاري بناء المشروع...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🔧 النظام الجديد يدعم:
echo    ✅ حفظ إعدادات قاعدة البيانات محلياً
echo    ✅ التعرف التلقائي على قواعد البيانات الموجودة
echo    ✅ نقل النظام بين الأجهزة بسهولة
echo    ✅ إعداد قاعدة البيانات عند التشغيل الأول
echo.

echo 🎯 جاري تشغيل النظام...
echo ℹ️  إذا كان هذا التشغيل الأول، ستظهر نافذة إعداد قاعدة البيانات
echo.

dotnet run --configuration Release

echo.
echo 📝 تم إغلاق النظام
pause
