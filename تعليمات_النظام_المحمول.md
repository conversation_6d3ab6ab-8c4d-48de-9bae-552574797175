# نظام إدارة الزيارات الميدانية - الإصدار المحمول

## 🎯 المشكلة التي تم حلها
**المشكلة السابقة**: عند نقل النظام لجهاز آخر، كان يطلب تسجيل مستخدم جديد ولا يتعرف على قاعدة البيانات الموجودة.

**الحل الجديد**: النظام الآن يحفظ إعدادات قاعدة البيانات محلياً ويتعرف عليها تلقائياً عند النقل لأي جهاز.

## ✨ المميزات الجديدة

### 1. الإعدادات المحمولة
- يتم حفظ إعدادات قاعدة البيانات في ملف `Data/database-config.json`
- عند نسخ النظام لجهاز آخر، تنتقل الإعدادات معه
- لا حاجة لإعادة إعداد قاعدة البيانات في كل مرة

### 2. الإعداد التلقائي
- عند التشغيل الأول، يظهر النظام نافذة إعداد قاعدة البيانات
- يتم حفظ الإعدادات تلقائياً للاستخدام المستقبلي
- اختبار الاتصال التلقائي قبل بدء النظام

### 3. التعامل مع الأخطاء
- إذا فشل الاتصال بقاعدة البيانات، يعرض النظام نافذة الإعداد
- رسائل واضحة لتوجيه المستخدم
- إمكانية إعادة الإعداد في أي وقت

## 🚀 طريقة الاستخدام

### للتشغيل الأول
1. شغل الملف `run_portable.bat`
2. ستظهر نافذة إعداد قاعدة البيانات
3. أدخل بيانات الخادم:
   - **اسم الخادم**: localhost (أو اسم الخادم المطلوب)
   - **اسم قاعدة البيانات**: SFDSYS
   - **نوع المصادقة**: Windows Authentication (مفضل)
4. اضغط "اختبار الاتصال" للتأكد
5. اضغط "حفظ الإعدادات"
6. سيبدأ النظام تلقائياً

### عند النقل لجهاز آخر
1. انسخ مجلد النظام كاملاً (مع مجلد Data)
2. شغل `run_portable.bat`
3. النظام سيتعرف على الإعدادات المحفوظة تلقائياً
4. إذا لم يتمكن من الاتصال، ستظهر نافذة الإعداد

## 📁 الملفات المهمة

### ملف الإعدادات
```
Data/database-config.json
```
هذا الملف يحتوي على إعدادات قاعدة البيانات ويجب نسخه مع النظام.

### ملفات التشغيل
- `run_portable.bat` - للتشغيل العادي مع الإعدادات المحمولة
- `run_sqlserver.bat` - للتشغيل مع SQL Server (الطريقة القديمة)

## 🔧 إعدادات قاعدة البيانات

### الإعدادات الافتراضية
```json
{
  "ServerName": "localhost",
  "DatabaseName": "SFDSYS", 
  "UseWindowsAuth": true,
  "Username": "",
  "Password": ""
}
```

### تغيير الإعدادات
1. احذف ملف `Data/database-config.json`
2. شغل النظام مرة أخرى
3. ستظهر نافذة الإعداد لإدخال إعدادات جديدة

## ⚠️ ملاحظات مهمة

### متطلبات النظام
- SQL Server يجب أن يكون مثبت ويعمل
- .NET 9.0 Runtime
- صلاحيات الوصول لقاعدة البيانات

### نصائح للاستخدام
1. **احتفظ بنسخة احتياطية** من مجلد Data دائماً
2. **تأكد من تشغيل SQL Server** قبل تشغيل النظام
3. **استخدم Windows Authentication** إذا أمكن (أكثر أماناً)
4. **لا تحذف ملف database-config.json** إلا إذا كنت تريد إعادة الإعداد

### حل المشاكل الشائعة

#### "لا يمكن الاتصال بقاعدة البيانات"
- تأكد من تشغيل SQL Server
- تحقق من اسم الخادم
- تأكد من صلاحيات الوصول

#### "يطلب تسجيل مستخدم جديد"
- تأكد من وجود ملف `Data/database-config.json`
- تحقق من صحة إعدادات قاعدة البيانات
- تأكد من وجود بيانات في قاعدة البيانات

#### "النظام بطيء في البدء"
- هذا طبيعي في التشغيل الأول (إنشاء الجداول)
- التشغيلات التالية ستكون أسرع

## 📞 الدعم الفني

إذا واجهت أي مشاكل:
1. تحقق من ملف السجل في النظام
2. تأكد من إعدادات SQL Server
3. جرب حذف ملف الإعدادات وإعادة الإعداد
4. تواصل مع فريق التطوير

---
**الإصدار**: 2.1.0 - النسخة المحمولة  
**التاريخ**: يوليو 2025  
**المطور**: فريق الصندوق الاجتماعي للتنمية
