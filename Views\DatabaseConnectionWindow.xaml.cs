using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// واجهة إعداد اتصال قاعدة البيانات الاحترافية
    /// </summary>
    public partial class DatabaseConnectionWindow : Window, INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        private ObservableCollection<TableInfo> _tables = new();
        public ObservableCollection<TableInfo> Tables
        {
            get => _tables;
            set
            {
                _tables = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(Tables)));
            }
        }

        public bool ConnectionSuccessful { get; private set; }
        public string ConnectionString { get; private set; } = string.Empty;

        public DatabaseConnectionWindow()
        {
            InitializeComponent();
            TablesDataGrid.ItemsSource = Tables;
            LoadCurrentSettings();
            SetupEventHandlers();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // تحميل الإعدادات من الملف المحلي أو متغيرات البيئة
                var settings = Data.DatabaseConfig.LoadSettings();

                ServerNameTextBox.Text = settings.ServerName;
                DatabaseNameTextBox.Text = settings.DatabaseName;

                if (settings.UseWindowsAuth)
                {
                    WindowsAuthRadio.IsChecked = true;
                }
                else
                {
                    SqlAuthRadio.IsChecked = true;
                    UsernameTextBox.Text = settings.Username;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم تحميل إعدادات قاعدة البيانات");
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus($"❌ خطأ في تحميل الإعدادات: {ex.Message}", false);
            }
        }

        private void SetupEventHandlers()
        {
            WindowsAuthRadio.Checked += (s, e) => ToggleAuthenticationFields(false);
            SqlAuthRadio.Checked += (s, e) => ToggleAuthenticationFields(true);
        }

        private void ToggleAuthenticationFields(bool enableSqlAuth)
        {
            UsernameTextBox.IsEnabled = enableSqlAuth;
            PasswordBox.IsEnabled = enableSqlAuth;
            UsernameLabel.Opacity = enableSqlAuth ? 1.0 : 0.5;
            PasswordLabel.Opacity = enableSqlAuth ? 1.0 : 0.5;
        }

        private async void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            await TestConnection();
        }

        private async Task TestConnection()
        {
            try
            {
                UpdateConnectionStatus("⏳ جاري اختبار الاتصال...", null);
                ConnectionProgressBar.Visibility = Visibility.Visible;
                ConnectionProgressBar.IsIndeterminate = true;

                var connectionString = BuildConnectionString();
                
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                
                // اختبار إنشاء قاعدة البيانات إذا لم تكن موجودة
                await EnsureDatabaseExists(connection);
                
                ConnectionString = connectionString;
                ConnectionSuccessful = true;
                
                UpdateConnectionStatus("✅ تم الاتصال بنجاح!", true);
                
                // تحديث قائمة الجداول
                await RefreshTables();
            }
            catch (Exception ex)
            {
                ConnectionSuccessful = false;
                UpdateConnectionStatus($"❌ فشل الاتصال: {ex.Message}", false);
            }
            finally
            {
                ConnectionProgressBar.Visibility = Visibility.Collapsed;
                ConnectionProgressBar.IsIndeterminate = false;
            }
        }

        private string BuildConnectionString()
        {
            var builder = new SqlConnectionStringBuilder();
            
            builder.DataSource = ServerNameTextBox.Text.Trim();
            builder.InitialCatalog = DatabaseNameTextBox.Text.Trim();
            builder.ConnectTimeout = 30;
            builder.CommandTimeout = 60;
            builder.TrustServerCertificate = true;

            if (WindowsAuthRadio.IsChecked == true)
            {
                builder.IntegratedSecurity = true;
            }
            else
            {
                builder.UserID = UsernameTextBox.Text.Trim();
                builder.Password = PasswordBox.Password;
            }

            return builder.ConnectionString;
        }

        private async Task EnsureDatabaseExists(SqlConnection connection)
        {
            var databaseName = DatabaseNameTextBox.Text.Trim();

            UpdateConnectionStatus("🔍 جاري التحقق من وجود قاعدة البيانات...", null);

            // التحقق من وجود قاعدة البيانات
            var checkDbQuery = $"SELECT COUNT(*) FROM sys.databases WHERE name = '{databaseName}'";
            using var checkCmd = new SqlCommand(checkDbQuery, connection);
            var dbExists = (int)await checkCmd.ExecuteScalarAsync() > 0;

            if (!dbExists)
            {
                // سؤال المستخدم عن إنشاء قاعدة البيانات
                var result = MessageBox.Show(
                    $"قاعدة البيانات '{databaseName}' غير موجودة.\n\nهل تريد إنشاءها الآن؟\n\n" +
                    "سيتم إنشاء قاعدة بيانات جديدة مع جميع الجداول والبيانات الأساسية.",
                    "إنشاء قاعدة بيانات جديدة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    UpdateConnectionStatus("⏳ جاري إنشاء قاعدة البيانات...", null);

                    // إنشاء قاعدة البيانات
                    var createDbQuery = $"CREATE DATABASE [{databaseName}]";
                    using var createCmd = new SqlCommand(createDbQuery, connection);
                    await createCmd.ExecuteNonQueryAsync();

                    UpdateConnectionStatus($"✅ تم إنشاء قاعدة البيانات '{databaseName}' بنجاح!", true);

                    MessageBox.Show(
                        $"تم إنشاء قاعدة البيانات '{databaseName}' بنجاح!\n\n" +
                        "سيتم الآن إنشاء الجداول والبيانات الأساسية عند بدء النظام.",
                        "نجح الإنشاء",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
                else
                {
                    throw new Exception($"قاعدة البيانات '{databaseName}' غير موجودة ولم يتم إنشاؤها.");
                }
            }
            else
            {
                UpdateConnectionStatus($"✅ تم العثور على قاعدة البيانات '{databaseName}'", true);
            }
        }

        private async void RefreshTablesButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshTables();
        }

        private async Task RefreshTables()
        {
            if (!ConnectionSuccessful)
            {
                MessageBox.Show("يرجى اختبار الاتصال أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                Tables.Clear();
                TablesCountText.Text = "⏳ جاري تحديث القائمة...";

                using var connection = new SqlConnection(ConnectionString);
                await connection.OpenAsync();

                // الحصول على قائمة الجداول
                var tablesQuery = @"
                    SELECT
                        TABLE_NAME as TableName,
                        0 as RowCount
                    FROM INFORMATION_SCHEMA.TABLES
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_NAME";

                using var cmd = new SqlCommand(tablesQuery, connection);
                using var reader = await cmd.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    var tableName = reader.GetString("TableName");

                    // حساب عدد الصفوف لكل جدول
                    long rowCount = 0;
                    try
                    {
                        using var countCmd = new SqlCommand($"SELECT COUNT(*) FROM [{tableName}]", connection);
                        var result = await countCmd.ExecuteScalarAsync();
                        rowCount = Convert.ToInt64(result);
                    }
                    catch
                    {
                        rowCount = 0; // في حالة وجود خطأ
                    }

                    Tables.Add(new TableInfo
                    {
                        TableName = tableName,
                        RowCount = rowCount,
                        Status = rowCount > 0 ? "يحتوي على بيانات" : "فارغ"
                    });
                }

                TablesCountText.Text = $"عدد الجداول: {Tables.Count}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث قائمة الجداول: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                TablesCountText.Text = "خطأ في التحديث";
            }
        }

        private async void CreateDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateConnectionStatus("⏳ جاري إنشاء الجداول...", null);
                
                var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseSqlServer(ConnectionString)
                    .Options;

                using var context = new ApplicationDbContext(options);
                
                // إنشاء الجداول
                await context.Database.EnsureCreatedAsync();
                
                UpdateConnectionStatus("✅ تم إنشاء جميع الجداول بنجاح!", true);
                
                // تحديث قائمة الجداول
                await RefreshTables();
                
                MessageBox.Show("تم إنشاء جميع الجداول بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus($"❌ فشل إنشاء الجداول: {ex.Message}", false);
                MessageBox.Show($"خطأ في إنشاء الجداول: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ConnectionSuccessful)
            {
                MessageBox.Show("يرجى اختبار الاتصال أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // إنشاء كائن الإعدادات الجديد
                var settings = new Data.DatabaseConfig.DatabaseSettings
                {
                    ServerName = ServerNameTextBox.Text.Trim(),
                    DatabaseName = DatabaseNameTextBox.Text.Trim(),
                    UseWindowsAuth = WindowsAuthRadio.IsChecked == true,
                    Username = SqlAuthRadio.IsChecked == true ? UsernameTextBox.Text.Trim() : "",
                    Password = SqlAuthRadio.IsChecked == true ? PasswordBox.Password : ""
                };

                // حفظ الإعدادات في الملف المحلي
                Data.DatabaseConfig.SaveSettings(settings);

                // حفظ الإعدادات في متغيرات البيئة أيضاً للتوافق
                Environment.SetEnvironmentVariable("SQL_SERVER_NAME", settings.ServerName);
                Environment.SetEnvironmentVariable("SQL_DATABASE_NAME", settings.DatabaseName);
                Environment.SetEnvironmentVariable("SQL_USE_WINDOWS_AUTH", settings.UseWindowsAuth.ToString());

                if (!settings.UseWindowsAuth)
                {
                    Environment.SetEnvironmentVariable("SQL_USERNAME", settings.Username);
                    // ملاحظة: كلمة المرور لا يتم حفظها في متغيرات البيئة لأسباب أمنية
                }

                MessageBox.Show("تم حفظ إعدادات قاعدة البيانات بنجاح!\nسيتم استخدام هذه الإعدادات عند نقل النظام لأي جهاز آخر.", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void UpdateConnectionStatus(string message, bool? success)
        {
            ConnectionStatusText.Text = message;
            
            if (success.HasValue)
            {
                ConnectionStatusText.Foreground = success.Value ? 
                    System.Windows.Media.Brushes.Green : 
                    System.Windows.Media.Brushes.Red;
            }
            else
            {
                ConnectionStatusText.Foreground = System.Windows.Media.Brushes.Orange;
            }
        }
    }

    /// <summary>
    /// معلومات الجدول
    /// </summary>
    public class TableInfo
    {
        public string TableName { get; set; } = string.Empty;
        public long RowCount { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
